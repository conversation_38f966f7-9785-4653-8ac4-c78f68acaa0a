# 🚀 GYMKOD PRO - PRODUCTION REDIS CACHE ALTYAPISI PLANI

## 📊 HEDEF SİSTEM
- **1000+ Spor Salonu**
- **100.000+ <PERSON>llanıcı**
- **10.000+ <PERSON>şzam<PERSON><PERSON>ı Aktif <PERSON>**
- **Response Time: <100ms**
- **Uptime: %99.9**

## 🖥️ SUNUCU DONANIM REQUİREMENTLARI

### Primary Server (Ana Sunucu)
```
CPU: 16 Core Intel Xeon Silver 4314 (2.4GHz)
RAM: 64GB DDR4 ECC
Storage: 2TB NVMe SSD (Samsung 980 PRO)
Network: 1Gbps Dedicated
OS: Windows Server 2022 Standard
```

### Backup Server (Yedek Sunucu - Opsiyonel)
```
CPU: 8 Core Intel Xeon Bronze 3204
RAM: 32GB DDR4 ECC  
Storage: 1TB NVMe SSD
Network: 1Gbps Shared
OS: Windows Server 2022 Standard
```

## 📈 MEMORY HESAPLAMALARI

### Cache Data Breakdown
```
Member Cache:
- 100.000 üye × 2KB (details) = 200MB
- 100.000 üye × 1KB (summary) = 100MB

Company Cache:
- 1000 salon × 5KB (settings) = 5MB
- 1000 salon × 10KB (membership types) = 10MB

System Cache:
- Cities/Towns: 5MB
- Exercises: 20MB
- System settings: 10MB

Session Cache:
- 10.000 aktif kullanıcı × 1KB = 10MB

Rate Limiting:
- Login attempts: 20MB
- API rate limits: 30MB

TOPLAM AKTİF DATA: ~410MB
REDIS MEMORY: 16GB (40x buffer + fragmentation)
```

## 🐳 DOCKER PRODUCTION SETUP

### docker-compose.production.yml
```yaml
version: '3.8'

services:
  redis-primary:
    image: redis:7.2-alpine
    container_name: gymkod-redis-primary
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
      - ./logs:/var/log/redis
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    networks:
      - gymkod-network
    deploy:
      resources:
        limits:
          memory: 16G
          cpus: '8'
        reservations:
          memory: 8G
          cpus: '4'

  redis-backup:
    image: redis:7.2-alpine
    container_name: gymkod-redis-backup
    restart: always
    ports:
      - "6380:6379"
    volumes:
      - redis_backup_data:/data
      - ./redis-backup.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf --slaveof redis-primary 6379
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    networks:
      - gymkod-network
    depends_on:
      - redis-primary

  redis-sentinel:
    image: redis:7.2-alpine
    container_name: gymkod-redis-sentinel
    restart: always
    ports:
      - "26379:26379"
    volumes:
      - ./sentinel.conf:/usr/local/etc/redis/sentinel.conf
    command: redis-sentinel /usr/local/etc/redis/sentinel.conf
    networks:
      - gymkod-network
    depends_on:
      - redis-primary
      - redis-backup

volumes:
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: D:\GymKod\Redis\Data
  redis_backup_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: D:\GymKod\Redis\Backup

networks:
  gymkod-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

## ⚙️ REDIS CONFIGURATION

### redis.conf (Production)
```conf
# NETWORK
bind 0.0.0.0
port 6379
protected-mode yes
requirepass ${REDIS_PASSWORD}

# MEMORY
maxmemory 14gb
maxmemory-policy allkeys-lru
maxmemory-samples 10

# PERSISTENCE
save 900 1
save 300 10
save 60 10000
rdbcompression yes
rdbchecksum yes
dbfilename gymkod-dump.rdb
dir /data

# APPEND ONLY FILE
appendonly yes
appendfilename "gymkod-appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# LOGGING
loglevel notice
logfile /var/log/redis/redis-server.log
syslog-enabled yes
syslog-ident redis

# PERFORMANCE
tcp-keepalive 300
timeout 0
tcp-backlog 511
databases 16

# SECURITY
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command EVAL ""
rename-command DEBUG ""
```

## 🔒 GÜVENLİK YAPISI

### SSL/TLS Configuration
```conf
# TLS/SSL
port 0
tls-port 6380
tls-cert-file /etc/ssl/certs/redis.crt
tls-key-file /etc/ssl/private/redis.key
tls-ca-cert-file /etc/ssl/certs/ca.crt
tls-protocols "TLSv1.2 TLSv1.3"
```

### Firewall Rules (Windows Server)
```powershell
# Redis port sadece uygulama sunucusundan erişilebilir
New-NetFirewallRule -DisplayName "Redis Primary" -Direction Inbound -Protocol TCP -LocalPort 6379 -RemoteAddress "*************" -Action Allow
New-NetFirewallRule -DisplayName "Redis Backup" -Direction Inbound -Protocol TCP -LocalPort 6380 -RemoteAddress "*************" -Action Allow
New-NetFirewallRule -DisplayName "Redis Sentinel" -Direction Inbound -Protocol TCP -LocalPort 26379 -RemoteAddress "*************" -Action Allow
```

## 📊 MONİTORİNG & ALERTING

### Redis Monitoring Stack
```yaml
  redis-exporter:
    image: oliver006/redis_exporter
    container_name: gymkod-redis-exporter
    restart: always
    ports:
      - "9121:9121"
    environment:
      - REDIS_ADDR=redis://redis-primary:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    networks:
      - gymkod-network

  prometheus:
    image: prom/prometheus
    container_name: gymkod-prometheus
    restart: always
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    networks:
      - gymkod-network

  grafana:
    image: grafana/grafana
    container_name: gymkod-grafana
    restart: always
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - gymkod-network
```

## 🔄 BACKUP STRATEJİSİ

### Otomatik Backup Script (PowerShell)
```powershell
# backup-redis.ps1
$BackupPath = "D:\GymKod\Backups\Redis"
$Date = Get-Date -Format "yyyyMMdd-HHmmss"
$BackupFile = "$BackupPath\redis-backup-$Date.rdb"

# Redis BGSAVE komutu
docker exec gymkod-redis-primary redis-cli -a $env:REDIS_PASSWORD BGSAVE

# Backup dosyasını kopyala
Start-Sleep -Seconds 30
docker cp gymkod-redis-primary:/data/gymkod-dump.rdb $BackupFile

# 7 günden eski backup'ları sil
Get-ChildItem $BackupPath -Filter "*.rdb" | Where-Object {$_.CreationTime -lt (Get-Date).AddDays(-7)} | Remove-Item

# Cloud backup (Azure/AWS)
# az storage blob upload --file $BackupFile --container-name redis-backups
```

### Scheduled Task (Windows)
```powershell
# Her 6 saatte bir backup al
$Action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File D:\GymKod\Scripts\backup-redis.ps1"
$Trigger = New-ScheduledTaskTrigger -Daily -At "00:00" -RepetitionInterval (New-TimeSpan -Hours 6)
$Settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
Register-ScheduledTask -TaskName "GymKod Redis Backup" -Action $Action -Trigger $Trigger -Settings $Settings
```

## 🚀 DEPLOYMENT ADIMLARI

### 1. Windows Server Hazırlık
```powershell
# Docker Desktop kurulumu
winget install Docker.DockerDesktop

# Gerekli dizinleri oluştur
New-Item -ItemType Directory -Path "D:\GymKod\Redis\Data" -Force
New-Item -ItemType Directory -Path "D:\GymKod\Redis\Backup" -Force
New-Item -ItemType Directory -Path "D:\GymKod\Redis\Logs" -Force
New-Item -ItemType Directory -Path "D:\GymKod\Backups\Redis" -Force

# Environment variables
[Environment]::SetEnvironmentVariable("REDIS_PASSWORD", "YourSecurePassword123!", "Machine")
[Environment]::SetEnvironmentVariable("GRAFANA_PASSWORD", "YourGrafanaPassword123!", "Machine")
```

### 2. Redis Deployment
```bash
# Production ortamında deploy et
docker-compose -f docker-compose.production.yml up -d

# Health check
docker exec gymkod-redis-primary redis-cli -a $REDIS_PASSWORD ping

# Memory kullanımını kontrol et
docker exec gymkod-redis-primary redis-cli -a $REDIS_PASSWORD info memory
```

### 3. .NET Application Configuration
```json
{
  "Redis": {
    "ConnectionString": "localhost:6379,password=YourSecurePassword123!,ssl=false,abortConnect=false",
    "DefaultExpiry": "00:30:00",
    "KeyPrefix": "gym",
    "MaxRetries": 3,
    "RetryDelay": "00:00:01"
  }
}
```

## 📈 PERFORMANCE TARGETS

### Beklenen Metrikler
```
Response Time:
- Cache Hit: <50ms
- Cache Miss: <200ms
- Database Query: <500ms

Throughput:
- 15.000+ requests/second
- 100.000+ cache operations/second

Memory:
- Redis Memory Usage: <12GB
- Cache Hit Ratio: >95%
- Memory Fragmentation: <1.5

Availability:
- Uptime: %99.9
- Failover Time: <30 seconds
- Recovery Time: <5 minutes
```

## 🔧 TROUBLESHOOTING

### Yaygın Sorunlar ve Çözümleri
```
1. Memory Pressure:
   - maxmemory-policy ayarını kontrol et
   - Gereksiz cache'leri temizle
   - Memory fragmentation'ı izle

2. Slow Queries:
   - SLOWLOG komutunu kullan
   - Key pattern'lerini optimize et
   - Pagination kullan

3. Connection Issues:
   - Connection pool ayarlarını kontrol et
   - Network latency'yi ölç
   - Firewall kurallarını doğrula

4. High CPU Usage:
   - Redis CPU kullanımını izle
   - Expensive operations'ları tespit et
   - Background save frequency'yi ayarla
```

Bu plan 1000+ spor salonu ve 100.000+ kullanıcı için optimize edilmiştir. Şimdi bu planı adım adım uygulayalım mı?
