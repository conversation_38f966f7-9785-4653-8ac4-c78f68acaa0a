🚀 WINDOWS SERVER 2019 NATIVE REDİS KURULUM REHBERİ
================================================================

SUNUCU BİLGİLERİ:
- OS: Windows Server 2019
- RAM: 4GB (Redis için 2GB ayrılacak)
- CPU: 2 Core
- Mevcut Kullanım: 1 salon, 100 üye
- Hedef: Ölçeklenebilir altyapı

KURULUM SIRASI:
1. Native Redis for Windows Kurulumu
2. Windows Service Konfigürasyonu
3. Production Configuration
4. Monitoring & Backup Setup
5. .NET Connection Test

================================================================
ADIM 1: NATIVE REDIS FOR WINDOWS KURULUMU
================================================================

1.1) PowerShell'i Administrator olarak açın ve şu komutları çalıştırın:

# Chocolatey package manager'ı kur (Redis kurulumu için)
Set-ExecutionPolicy Bypass -Scope Process -Force
[System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# Redis'i Chocolatey ile kur
choco install redis-64 -y

1.2) Alternatif: Manuel Redis kurulumu (Chocolatey başarısız olursa):

# Redis for Windows'u GitHub'dan indir (Memurai - Redis uyumlu)
$redisUrl = "https://www.memurai.com/get-memurai"
Write-Host "Manuel kurulum için şu adrese gidin: $redisUrl"
Write-Host "Memurai Developer Edition (Ücretsiz) indirin ve kurun"

1.3) Redis kurulumunu doğrula:

# Redis servisinin durumunu kontrol et
Get-Service -Name "*Redis*" -ErrorAction SilentlyContinue
Get-Service -Name "*Memurai*" -ErrorAction SilentlyContinue

# Redis CLI test et (kurulum yoluna göre)
redis-cli ping
# veya
memurai-cli ping

================================================================
ADIM 2: GYMPROJECT KLASÖR YAPISINI OLUŞTUR
================================================================

# Ana klasörü oluştur
New-Item -ItemType Directory -Path "C:\GymProject" -Force
New-Item -ItemType Directory -Path "C:\GymProject\redis" -Force
New-Item -ItemType Directory -Path "C:\GymProject\logs" -Force
New-Item -ItemType Directory -Path "C:\GymProject\backup" -Force
New-Item -ItemType Directory -Path "C:\GymProject\monitoring" -Force

Set-Location "C:\GymProject"

================================================================
ADIM 3: REDIS PRODUCTION CONFIGURATION
================================================================

3.1) Redis konfigürasyon dosyasını oluşturun:

@"
# Redis Production Configuration for GymProject
# Windows Server 2019 - 4GB RAM, 2CPU

# Network
bind 127.0.0.1
port 6379
protected-mode yes

# Authentication
requirepass GymProject2024Redis!Strong

# Memory Management (2GB max)
maxmemory 2gb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000

# AOF Persistence
appendonly yes
appendfsync everysec
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# Logging
loglevel notice
logfile C:/GymProject/logs/redis-server.log

# Performance
tcp-keepalive 300
timeout 300
tcp-backlog 511

# Security
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""
rename-command CONFIG "CONFIG_b835729c9c"

# Database
databases 16

# Slow log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Windows specific
dir C:/GymProject/redis/
dbfilename dump.rdb
appendfilename appendonly.aof
"@ | Out-File -FilePath "C:\GymProject\redis\redis.conf" -Encoding UTF8

================================================================
ADIM 4: REDIS WINDOWS SERVICE KONFIGÜRASYONU
================================================================

4.1) Redis servisini durdur ve yeniden yapılandır:

# Mevcut Redis servisini durdur
Stop-Service -Name "Redis" -ErrorAction SilentlyContinue
Stop-Service -Name "Memurai" -ErrorAction SilentlyContinue

4.2) Redis'i custom config ile başlat:

# Redis servisini custom config ile başlatmak için batch dosyası oluştur
@"
@echo off
cd /d C:\GymProject\redis
redis-server.exe C:\GymProject\redis\redis.conf
"@ | Out-File -FilePath "C:\GymProject\redis\start-redis.bat" -Encoding ASCII

# Redis'i Windows Service olarak kaydet (NSSM kullanarak)
# NSSM (Non-Sucking Service Manager) indir ve kur
$nssmUrl = "https://nssm.cc/release/nssm-2.24.zip"
$nssmZip = "$env:TEMP\nssm.zip"
$nssmPath = "C:\GymProject\nssm"

Invoke-WebRequest -Uri $nssmUrl -OutFile $nssmZip
Expand-Archive -Path $nssmZip -DestinationPath $nssmPath -Force

# NSSM ile Redis servisini oluştur
& "$nssmPath\nssm-2.24\win64\nssm.exe" install GymProjectRedis "C:\GymProject\redis\start-redis.bat"
& "$nssmPath\nssm-2.24\win64\nssm.exe" set GymProjectRedis Description "GymProject Redis Server"
& "$nssmPath\nssm-2.24\win64\nssm.exe" set GymProjectRedis Start SERVICE_AUTO_START

# Servisi başlat
Start-Service -Name "GymProjectRedis"

================================================================
ADIM 5: REDIS BAĞLANTI TESTİ
================================================================

5.1) Redis bağlantısını test edin:

# Redis CLI ile test
redis-cli -a "GymProject2024Redis!Strong" ping

# Test verileri ekle
redis-cli -a "GymProject2024Redis!Strong" set test "Hello GymProject"
redis-cli -a "GymProject2024Redis!Strong" get test

# Memory bilgilerini kontrol et
redis-cli -a "GymProject2024Redis!Strong" info memory

================================================================
ADIM 6: FIREWALL AYARLARI
================================================================

# Redis portu için firewall kuralı
New-NetFirewallRule -DisplayName "GymProject Redis" -Direction Inbound -Protocol TCP -LocalPort 6379 -Action Allow

================================================================
ADIM 7: MONITORING VE BACKUP SCRIPT'LERİ
================================================================

7.1) Backup script'i oluşturun:

@"
param(
    [string]`$BackupPath = "C:\GymProject\backup",
    [int]`$RetentionDays = 7
)

`$Date = Get-Date -Format "yyyyMMdd_HHmmss"
`$BackupFile = "`$BackupPath\redis-backup-`$Date.rdb"

Write-Host "Redis backup başlatılıyor: `$BackupFile"

# Redis BGSAVE komutu
redis-cli -a "GymProject2024Redis!Strong" BGSAVE
Start-Sleep -Seconds 10

# Backup dosyasını kopyala
Copy-Item "C:\GymProject\redis\dump.rdb" `$BackupFile

# Eski backup'ları temizle
Get-ChildItem `$BackupPath -Name "redis-backup-*.rdb" | 
    Where-Object { (Get-Date) - (Get-Item `$_).CreationTime -gt (New-TimeSpan -Days `$RetentionDays) } |
    Remove-Item

Write-Host "Backup tamamlandı: `$BackupFile"
"@ | Out-File -FilePath "C:\GymProject\backup\redis-backup.ps1" -Encoding UTF8

7.2) Health check script'i oluşturun:

@"
`$LogFile = "C:\GymProject\logs\redis-health.log"

function Write-Log {
    param([string]`$Message)
    `$Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    "`$Timestamp - `$Message" | Out-File -FilePath `$LogFile -Append
}

# Redis servisinin durumunu kontrol et
`$ServiceStatus = Get-Service -Name "GymProjectRedis" -ErrorAction SilentlyContinue

if (`$ServiceStatus -and `$ServiceStatus.Status -eq "Running") {
    # Redis ping testi
    try {
        `$PingResult = redis-cli -a "GymProject2024Redis!Strong" ping
        
        if (`$PingResult -eq "PONG") {
            Write-Log "Redis sağlıklı - Service: Running, Ping: `$PingResult"
            
            # Memory kullanımını kontrol et
            `$MemoryInfo = redis-cli -a "GymProject2024Redis!Strong" info memory | Select-String "used_memory_human"
            Write-Log "Memory Info: `$MemoryInfo"
            
            exit 0
        } else {
            Write-Log "HATA: Redis ping başarısız - `$PingResult"
            exit 1
        }
    } catch {
        Write-Log "HATA: Redis bağlantı hatası - `$(`$_.Exception.Message)"
        exit 1
    }
} else {
    Write-Log "HATA: Redis servisi çalışmıyor"
    
    # Servisi yeniden başlat
    Write-Log "Redis servisi yeniden başlatılıyor..."
    Restart-Service -Name "GymProjectRedis" -ErrorAction SilentlyContinue
    
    exit 1
}
"@ | Out-File -FilePath "C:\GymProject\monitoring\redis-health.ps1" -Encoding UTF8

================================================================
ADIM 8: SCHEDULED TASK'LER OLUŞTUR
================================================================

# Günlük backup task'i oluştur
$Action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\GymProject\backup\redis-backup.ps1"
$Trigger = New-ScheduledTaskTrigger -Daily -At "02:00AM"
$Settings = New-ScheduledTaskSettingsSet -ExecutionTimeLimit (New-TimeSpan -Hours 1)
Register-ScheduledTask -TaskName "GymProject Redis Daily Backup" -Action $Action -Trigger $Trigger -Settings $Settings -User "SYSTEM"

# Health check task'i (5 dakikada bir)
$Action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\GymProject\monitoring\redis-health.ps1"
$Trigger = New-ScheduledTaskTrigger -Once -At (Get-Date) -RepetitionInterval (New-TimeSpan -Minutes 5) -RepetitionDuration (New-TimeSpan -Days 365)
$Settings = New-ScheduledTaskSettingsSet -ExecutionTimeLimit (New-TimeSpan -Minutes 2)
Register-ScheduledTask -TaskName "GymProject Redis Health Check" -Action $Action -Trigger $Trigger -Settings $Settings -User "SYSTEM"

================================================================
SONRAKI ADIMLAR
================================================================

1. Bu script'i çalıştırdıktan sonra Redis'in çalıştığını doğrulayın
2. .NET uygulamanızın appsettings.json'ında connection string'i güncelleyin
3. Production test'lerini yapın
4. Performance monitoring'i aktifleştirin

BAĞLANTI STRING'İ:
"Redis": {
  "Production": "localhost:6379,password=GymProject2024Redis!Strong,ssl=false,abortConnect=false"
}
